"""
正文写作节点
"""
import os
from typing import Dict, Any
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate

from article_state import ArticleState, update_state


class ContentGenerator:
    """正文生成器"""
    
    def __init__(self, model_name: str = "gpt-3.5-turbo"):
        self.llm = ChatOpenAI(
            model=model_name,
            temperature=0.7,
            api_key=os.getenv("OPENAI_API_KEY"),
            base_url=os.getenv("OPENAI_BASE_URL")
        )
        
        self.prompt_template = ChatPromptTemplate.from_messages([
            ("system", """你是一位专业的内容创作者，擅长写作高质量的文章。

请根据给定的主题和标题，创作一篇结构清晰、内容丰富的文章正文。

文章要求：
1. 结构清晰：有引言、主体段落、结论
2. 内容充实：每个段落都有具体的论述和例子
3. 语言流畅：通俗易懂，避免过于专业的术语
4. 逻辑性强：段落之间有清晰的逻辑关系
5. 字数适中：800-1200字左右

文章结构建议：
- 开头：引出话题，吸引读者注意
- 主体：2-4个段落，每段一个要点
- 结尾：总结观点，给出建议或展望

请直接输出文章正文，不需要额外的格式说明。"""),
            ("human", """主题：{topic}

参考标题：
{title}

请基于以上信息创作文章正文：""")
        ])
    
    def generate_content(self, topic: str, title: str) -> str:
        """生成正文"""
        try:
            messages = self.prompt_template.format_messages(
                topic=topic,
                title=title
            )
            response = self.llm.invoke(messages)
            return response.content
        except Exception as e:
            return f"正文生成失败：{str(e)}"


def content_node(state: ArticleState) -> ArticleState:
    """正文生成节点"""
    print(f"📝 开始生成正文，主题：{state['topic']}")
    
    try:
        # 检查是否有标题
        if not state.get("title"):
            error_msg = "缺少标题信息，无法生成正文"
            print(f"❌ {error_msg}")
            
            errors = state.get("errors", []) or []
            errors.append(error_msg)
            
            return update_state(
                state,
                errors=errors,
                current_step="content_error"
            )
        
        # 初始化正文生成器
        generator = ContentGenerator()
        
        # 生成正文
        content_text = generator.generate_content(
            state["topic"], 
            state["title"]
        )
        
        # 更新状态
        new_state = update_state(
            state,
            content=content_text,
            current_step="content_generated"
        )
        
        print(f"✅ 正文生成完成")
        print(f"正文长度：{len(content_text)}字")
        
        return new_state
        
    except Exception as e:
        error_msg = f"正文生成节点错误：{str(e)}"
        print(f"❌ {error_msg}")
        
        errors = state.get("errors", []) or []
        errors.append(error_msg)
        
        return update_state(
            state,
            errors=errors,
            current_step="content_error"
        )
