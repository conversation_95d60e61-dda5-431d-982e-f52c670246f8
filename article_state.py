"""
文章写作智能体系统的状态定义
"""
from typing import Optional, List
from pydantic import BaseModel, Field
from typing_extensions import TypedDict


class ArticleState(TypedDict):
    """文章写作流程的状态管理"""
    
    # 基础信息
    topic: str  # 文章主题
    
    # 各个阶段的输出
    title: Optional[str]  # 生成的标题
    content: Optional[str]  # 文章正文
    summary: Optional[str]  # 文章摘要
    image_paths: Optional[List[str]]  # 插图路径列表
    
    # 流程控制
    current_step: str  # 当前执行步骤
    is_complete: bool  # 是否完成所有步骤
    
    # 错误处理
    errors: Optional[List[str]]  # 错误信息列表


class ArticleConfig(BaseModel):
    """文章生成配置"""
    
    # 标题生成配置
    title_count: int = Field(default=3, description="生成标题数量")
    title_style: str = Field(default="爆款", description="标题风格：爆款、专业、简洁")
    
    # 正文生成配置
    content_length: str = Field(default="中等", description="文章长度：短、中等、长")
    content_style: str = Field(default="通俗易懂", description="写作风格")
    
    # 摘要生成配置
    summary_length: int = Field(default=200, description="摘要字数限制")
    
    # 插图配置
    image_count: int = Field(default=3, description="插图数量")
    image_style: str = Field(default="现代简约", description="插图风格")


def create_initial_state(topic: str, config: Optional[ArticleConfig] = None) -> ArticleState:
    """创建初始状态"""
    if config is None:
        config = ArticleConfig()
    
    return ArticleState(
        topic=topic,
        title=None,
        content=None,
        summary=None,
        image_paths=None,
        current_step="start",
        is_complete=False,
        errors=None
    )


def update_state(state: ArticleState, **updates) -> ArticleState:
    """更新状态"""
    new_state = state.copy()
    for key, value in updates.items():
        if key in new_state:
            new_state[key] = value
    return new_state
