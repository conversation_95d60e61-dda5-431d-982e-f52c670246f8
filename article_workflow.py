"""
文章写作智能体工作流
"""
from typing import Dict, Any
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from article_state import ArticleState, update_state
from nodes.title_node import title_node
from nodes.content_node import content_node
from nodes.summary_node import summary_node
from nodes.image_node import image_node


def should_continue(state: ArticleState) -> str:
    """决定工作流的下一步"""
    current_step = state.get("current_step", "start")
    errors = state.get("errors")
    
    # 如果有错误，结束流程
    if errors:
        print(f"⚠️ 检测到错误，终止流程：{errors}")
        return END
    
    # 根据当前步骤决定下一步
    if current_step == "start":
        return "generate_title"
    elif current_step == "title_generated":
        return "generate_content"
    elif current_step == "content_generated":
        return "generate_summary"
    elif current_step == "summary_generated":
        return "generate_images"
    elif current_step == "image_generated":
        return "complete"
    else:
        return END


def complete_node(state: ArticleState) -> ArticleState:
    """完成节点"""
    print("🎉 文章创作完成！")
    
    # 打印最终结果摘要
    print("\n" + "="*50)
    print("📄 文章创作结果摘要")
    print("="*50)
    print(f"主题：{state['topic']}")
    print(f"标题：{state.get('title', '未生成')[:100]}...")
    print(f"正文长度：{len(state.get('content', ''))}字")
    print(f"摘要长度：{len(state.get('summary', ''))}字")
    print(f"插图数量：{len(state.get('image_paths', []))}")
    print("="*50)
    
    return update_state(
        state,
        is_complete=True,
        current_step="completed"
    )


def create_article_workflow() -> StateGraph:
    """创建文章写作工作流"""
    
    # 创建状态图
    workflow = StateGraph(ArticleState)
    
    # 添加节点
    workflow.add_node("generate_title", title_node)
    workflow.add_node("generate_content", content_node)
    workflow.add_node("generate_summary", summary_node)
    workflow.add_node("generate_images", image_node)
    workflow.add_node("complete", complete_node)
    
    # 设置入口点
    workflow.set_entry_point("generate_title")
    
    # 添加条件边
    workflow.add_conditional_edges(
        "generate_title",
        should_continue,
        {
            "generate_content": "generate_content",
            END: END
        }
    )
    
    workflow.add_conditional_edges(
        "generate_content",
        should_continue,
        {
            "generate_summary": "generate_summary",
            END: END
        }
    )
    
    workflow.add_conditional_edges(
        "generate_summary",
        should_continue,
        {
            "generate_images": "generate_images",
            END: END
        }
    )
    
    workflow.add_conditional_edges(
        "generate_images",
        should_continue,
        {
            "complete": "complete",
            END: END
        }
    )
    
    # 完成节点直接结束
    workflow.add_edge("complete", END)
    
    return workflow


def compile_workflow() -> Any:
    """编译工作流"""
    workflow = create_article_workflow()
    
    # 添加内存检查点
    memory = MemorySaver()
    
    # 编译工作流
    app = workflow.compile(checkpointer=memory)
    
    return app


if __name__ == "__main__":
    # 测试工作流
    from article_state import create_initial_state
    
    # 创建应用
    app = compile_workflow()
    
    # 创建初始状态
    initial_state = create_initial_state("人工智能在教育领域的应用")
    
    # 运行工作流
    config = {"configurable": {"thread_id": "test_thread"}}
    
    print("🚀 开始文章创作流程...")
    print(f"主题：{initial_state['topic']}")
    print("-" * 50)
    
    try:
        # 执行工作流
        final_state = app.invoke(initial_state, config)
        
        print("\n✅ 工作流执行完成！")
        
    except Exception as e:
        print(f"\n❌ 工作流执行失败：{str(e)}")
        import traceback
        traceback.print_exc()
