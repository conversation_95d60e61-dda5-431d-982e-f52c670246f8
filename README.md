# 文章写作智能体系统

基于LangGraph框架开发的多智能体文章写作系统，能够自动完成从主题确定到文章发布的完整创作流程。

## 🚀 功能特性

- **智能标题生成**：根据主题生成多种风格的爆款标题
- **自动正文写作**：基于主题和标题创作结构化文章正文
- **摘要自动提取**：从正文中提炼核心要点生成摘要
- **插图建议生成**：根据文章内容推荐合适的插图
- **流程化管理**：使用LangGraph管理完整的创作工作流

## 📋 工作流程

```
开始 → 生成标题 → 写作正文 → 生成摘要 → 推荐插图 → 完成
```

## 🛠️ 系统架构

### 状态管理
- `ArticleState`：管理文章创作过程中的所有状态
- 包含：主题、标题、正文、摘要、插图路径等信息

### 核心节点
- **标题节点** (`title_node`)：生成爆款标题
- **正文节点** (`content_node`)：创作文章正文
- **摘要节点** (`summary_node`)：生成文章摘要
- **插图节点** (`image_node`)：推荐插图方案

## 📦 安装依赖

```bash
pip install -r requirements.txt
```

## ⚙️ 配置设置

1. 复制环境变量模板：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，设置您的API密钥：
```env
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
```

## 🎯 使用方法

### 1. 交互模式
```bash
python main.py
```

### 2. 命令行模式
```bash
python main.py "人工智能在教育领域的应用"
```

### 3. 编程调用
```python
from main import run_article_creation
from article_state import ArticleConfig

# 基础使用
result = run_article_creation("人工智能的发展趋势")

# 自定义配置
config = ArticleConfig(
    title_count=5,
    title_style="专业",
    content_length="长",
    summary_length=300
)
result = run_article_creation("区块链技术应用", config)
```

## 🧪 运行测试

```bash
python test_workflow.py
```

测试包括：
- 状态管理测试
- 单个节点功能测试
- 完整工作流测试

## 📁 输出文件

系统会在 `output/` 目录下生成：
- `{主题}_{时间戳}.json`：完整的结构化数据
- `{主题}_{时间戳}.txt`：可读的文本格式

## 🔧 自定义扩展

### 添加新节点
1. 在 `nodes/` 目录下创建新的节点文件
2. 实现节点函数，接收和返回 `ArticleState`
3. 在 `article_workflow.py` 中注册新节点

### 修改工作流
在 `article_workflow.py` 中的 `create_article_workflow()` 函数中：
- 添加新节点：`workflow.add_node("node_name", node_function)`
- 修改流程：调整 `should_continue()` 函数的逻辑

### 自定义提示词
在各个节点文件中修改 `prompt_template` 来调整AI的行为。

## 📊 系统监控

系统提供详细的执行日志：
- 🎯 标题生成进度
- 📝 正文写作状态
- 📋 摘要生成结果
- 🖼️ 插图推荐信息
- ✅ 完成状态确认

## ⚠️ 注意事项

1. **API密钥**：确保正确设置OpenAI API密钥
2. **网络连接**：需要稳定的网络连接访问AI服务
3. **费用控制**：根据使用量可能产生API调用费用
4. **内容质量**：生成的内容需要人工审核和编辑

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

## 🆘 常见问题

**Q: 如何更换AI模型？**
A: 在各节点文件中修改 `model_name` 参数，如改为 `"gpt-4"`

**Q: 如何调整文章长度？**
A: 修改 `ArticleConfig` 中的 `content_length` 参数

**Q: 插图功能如何实现？**
A: 当前版本提供插图建议，可扩展对接DALL-E等图片生成API

**Q: 如何批量处理多个主题？**
A: 可以编写脚本循环调用 `run_article_creation()` 函数
