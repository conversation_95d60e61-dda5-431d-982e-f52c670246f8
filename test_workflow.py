"""
文章写作智能体系统测试
"""
import os
import sys
from dotenv import load_dotenv

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from article_state import create_initial_state, ArticleConfig
from article_workflow import compile_workflow


def test_basic_workflow():
    """测试基本工作流"""
    print("🧪 开始测试基本工作流...")
    
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ 测试跳过：未设置OPENAI_API_KEY")
        return False
    
    try:
        # 创建工作流
        app = compile_workflow()
        print("✅ 工作流编译成功")
        
        # 创建测试状态
        test_topic = "人工智能在日常生活中的应用"
        initial_state = create_initial_state(test_topic)
        print(f"✅ 初始状态创建成功：{test_topic}")
        
        # 配置
        config = {"configurable": {"thread_id": "test_thread"}}
        
        # 执行工作流
        print("🚀 开始执行工作流...")
        final_state = app.invoke(initial_state, config)
        
        # 检查结果
        if final_state.get('is_complete'):
            print("✅ 工作流执行成功")
            print(f"   - 主题：{final_state['topic']}")
            print(f"   - 标题长度：{len(final_state.get('title', ''))}")
            print(f"   - 正文长度：{len(final_state.get('content', ''))}")
            print(f"   - 摘要长度：{len(final_state.get('summary', ''))}")
            print(f"   - 插图数量：{len(final_state.get('image_paths', []))}")
            return True
        else:
            print("❌ 工作流未完成")
            if final_state.get('errors'):
                print(f"   错误：{final_state['errors']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_state_management():
    """测试状态管理"""
    print("\n🧪 开始测试状态管理...")
    
    try:
        # 测试状态创建
        topic = "测试主题"
        state = create_initial_state(topic)
        
        assert state['topic'] == topic
        assert state['current_step'] == "start"
        assert state['is_complete'] == False
        assert state['title'] is None
        
        print("✅ 状态创建测试通过")
        
        # 测试状态更新
        from article_state import update_state
        
        updated_state = update_state(
            state,
            title="测试标题",
            current_step="title_generated"
        )
        
        assert updated_state['title'] == "测试标题"
        assert updated_state['current_step'] == "title_generated"
        assert updated_state['topic'] == topic  # 原有字段保持不变
        
        print("✅ 状态更新测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 状态管理测试失败：{str(e)}")
        return False


def test_individual_nodes():
    """测试单个节点"""
    print("\n🧪 开始测试单个节点...")
    
    # 加载环境变量
    load_dotenv()
    
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ 节点测试跳过：未设置OPENAI_API_KEY")
        return False
    
    try:
        # 测试标题节点
        from nodes.title_node import title_node
        
        test_state = create_initial_state("人工智能的未来发展")
        result_state = title_node(test_state)
        
        if result_state.get('title') and not result_state.get('errors'):
            print("✅ 标题节点测试通过")
        else:
            print("❌ 标题节点测试失败")
            return False
        
        # 测试正文节点
        from nodes.content_node import content_node
        
        content_result = content_node(result_state)
        
        if content_result.get('content') and not content_result.get('errors'):
            print("✅ 正文节点测试通过")
        else:
            print("❌ 正文节点测试失败")
            return False
        
        print("✅ 单个节点测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 单个节点测试失败：{str(e)}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("🧪 开始运行文章写作智能体系统测试套件")
    print("="*60)
    
    tests = [
        ("状态管理测试", test_state_management),
        ("单个节点测试", test_individual_nodes),
        ("基本工作流测试", test_basic_workflow),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "="*60)
    print(f"🧪 测试结果：{passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置和代码")
        return False


if __name__ == "__main__":
    run_all_tests()
