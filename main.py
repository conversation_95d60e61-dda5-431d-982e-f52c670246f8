"""
文章写作智能体系统主程序
"""
import os
import json
from datetime import datetime
from dotenv import load_dotenv

from article_state import create_initial_state, ArticleConfig
from article_workflow import compile_workflow


def save_article_result(state, output_dir="output"):
    """保存文章结果到文件"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 生成文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    topic_safe = state['topic'].replace(' ', '_').replace('/', '_')[:20]
    filename = f"{topic_safe}_{timestamp}"
    
    # 保存完整结果为JSON
    result_data = {
        "topic": state['topic'],
        "title": state.get('title'),
        "content": state.get('content'),
        "summary": state.get('summary'),
        "image_paths": state.get('image_paths'),
        "created_at": datetime.now().isoformat(),
        "is_complete": state.get('is_complete', False)
    }
    
    json_path = os.path.join(output_dir, f"{filename}.json")
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(result_data, f, ensure_ascii=False, indent=2)
    
    # 保存可读的文本版本
    txt_path = os.path.join(output_dir, f"{filename}.txt")
    with open(txt_path, 'w', encoding='utf-8') as f:
        f.write(f"文章主题：{state['topic']}\n")
        f.write("="*50 + "\n\n")
        
        if state.get('title'):
            f.write("标题：\n")
            f.write(state['title'] + "\n\n")
        
        if state.get('content'):
            f.write("正文：\n")
            f.write(state['content'] + "\n\n")
        
        if state.get('summary'):
            f.write("摘要：\n")
            f.write(state['summary'] + "\n\n")
        
        if state.get('image_paths'):
            f.write("插图路径：\n")
            for i, path in enumerate(state['image_paths'], 1):
                f.write(f"{i}. {path}\n")
    
    print(f"📁 文章结果已保存：")
    print(f"   JSON格式：{json_path}")
    print(f"   文本格式：{txt_path}")
    
    return json_path, txt_path


def run_article_creation(topic: str, config: ArticleConfig = None):
    """运行文章创作流程"""
    print("🚀 启动文章写作智能体系统")
    print("="*60)
    
    # 创建工作流应用
    app = compile_workflow()
    
    # 创建初始状态
    initial_state = create_initial_state(topic, config)
    
    # 配置
    workflow_config = {"configurable": {"thread_id": f"article_{datetime.now().strftime('%Y%m%d_%H%M%S')}"}}
    
    print(f"📝 开始创作文章")
    print(f"主题：{topic}")
    print("-" * 60)
    
    try:
        # 执行工作流
        final_state = app.invoke(initial_state, workflow_config)
        
        if final_state.get('is_complete'):
            print("\n🎉 文章创作成功完成！")
            
            # 保存结果
            save_article_result(final_state)
            
            return final_state
        else:
            print("\n⚠️ 文章创作未完全完成")
            if final_state.get('errors'):
                print(f"错误信息：{final_state['errors']}")
            return final_state
            
    except Exception as e:
        print(f"\n❌ 文章创作失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return None


def interactive_mode():
    """交互模式"""
    print("🤖 文章写作智能体系统 - 交互模式")
    print("="*60)
    
    while True:
        print("\n请选择操作：")
        print("1. 创作新文章")
        print("2. 查看历史文章")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            topic = input("\n请输入文章主题: ").strip()
            if topic:
                # 可选：询问配置
                print("\n使用默认配置创作文章...")
                result = run_article_creation(topic)
                
                if result:
                    print(f"\n✅ 文章《{topic}》创作完成！")
                else:
                    print(f"\n❌ 文章《{topic}》创作失败！")
            else:
                print("❌ 主题不能为空！")
        
        elif choice == "2":
            # 查看输出目录
            output_dir = "output"
            if os.path.exists(output_dir):
                files = [f for f in os.listdir(output_dir) if f.endswith('.txt')]
                if files:
                    print(f"\n📚 历史文章列表：")
                    for i, file in enumerate(files, 1):
                        print(f"{i}. {file}")
                else:
                    print("\n📭 暂无历史文章")
            else:
                print("\n📭 暂无历史文章")
        
        elif choice == "3":
            print("\n👋 再见！")
            break
        
        else:
            print("\n❌ 无效选择，请重新输入！")


def main():
    """主函数"""
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ 错误：未找到OPENAI_API_KEY环境变量")
        print("请在.env文件中设置您的OpenAI API密钥")
        return
    
    import sys
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        # 命令行模式
        topic = " ".join(sys.argv[1:])
        run_article_creation(topic)
    else:
        # 交互模式
        interactive_mode()


if __name__ == "__main__":
    main()
