"""
插图生成节点
"""
import os
from typing import Dict, Any, List
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate

from article_state import ArticleState, update_state


class ImageGenerator:
    """插图生成器"""
    
    def __init__(self, model_name: str = "gpt-3.5-turbo"):
        self.llm = ChatOpenAI(
            model=model_name,
            temperature=0.6,
            api_key=os.getenv("OPENAI_API_KEY"),
            base_url=os.getenv("OPENAI_BASE_URL")
        )
        
        self.prompt_template = ChatPromptTemplate.from_messages([
            ("system", """你是一位专业的视觉设计顾问，擅长为文章内容推荐合适的插图。

请根据给定的文章内容，推荐3-5个合适的插图描述。

插图要求：
1. 与文章内容高度相关
2. 能够增强文章的视觉效果
3. 适合网络传播和阅读
4. 风格统一，符合现代审美

对于每个插图，请提供：
- 插图位置建议（开头、中间段落、结尾等）
- 插图类型（图表、照片、插画、图标等）
- 具体描述（内容、风格、色彩等）
- 关键词（用于搜索或生成）

请按以下格式输出：
插图1：
位置：[位置建议]
类型：[插图类型]
描述：[具体描述]
关键词：[关键词1, 关键词2, 关键词3]

插图2：
...（以此类推）"""),
            ("human", """请为以下文章推荐插图：

标题：{title}

正文：
{content}

请推荐合适的插图：""")
        ])
    
    def generate_image_suggestions(self, title: str, content: str) -> str:
        """生成插图建议"""
        try:
            messages = self.prompt_template.format_messages(
                title=title,
                content=content
            )
            response = self.llm.invoke(messages)
            return response.content
        except Exception as e:
            return f"插图建议生成失败：{str(e)}"
    
    def parse_image_paths(self, suggestions: str) -> List[str]:
        """解析插图路径（这里模拟生成路径）"""
        # 在实际应用中，这里可以：
        # 1. 调用图片生成API（如DALL-E、Midjourney等）
        # 2. 从图片库中搜索匹配的图片
        # 3. 返回图片的URL或本地路径
        
        # 这里返回模拟的图片路径
        paths = []
        lines = suggestions.split('\n')
        image_count = 0
        
        for line in lines:
            if line.startswith('插图') and '：' in line:
                image_count += 1
                # 模拟图片路径
                path = f"images/article_image_{image_count}.jpg"
                paths.append(path)
        
        return paths if paths else ["images/default_image.jpg"]


def image_node(state: ArticleState) -> ArticleState:
    """插图生成节点"""
    print(f"🖼️ 开始生成插图建议")
    
    try:
        # 检查是否有正文
        if not state.get("content"):
            error_msg = "缺少正文内容，无法生成插图建议"
            print(f"❌ {error_msg}")
            
            errors = state.get("errors", []) or []
            errors.append(error_msg)
            
            return update_state(
                state,
                errors=errors,
                current_step="image_error"
            )
        
        # 初始化插图生成器
        generator = ImageGenerator()
        
        # 生成插图建议
        image_suggestions = generator.generate_image_suggestions(
            state.get("title", ""),
            state["content"]
        )
        
        # 解析插图路径
        image_paths = generator.parse_image_paths(image_suggestions)
        
        # 更新状态
        new_state = update_state(
            state,
            image_paths=image_paths,
            current_step="image_generated"
        )
        
        print(f"✅ 插图建议生成完成")
        print(f"建议插图数量：{len(image_paths)}")
        print(f"插图建议：\n{image_suggestions}")
        print(f"插图路径：{image_paths}")
        
        return new_state
        
    except Exception as e:
        error_msg = f"插图生成节点错误：{str(e)}"
        print(f"❌ {error_msg}")
        
        errors = state.get("errors", []) or []
        errors.append(error_msg)
        
        return update_state(
            state,
            errors=errors,
            current_step="image_error"
        )
