"""
标题生成节点
"""
import os
from typing import Dict, Any
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain.schema import HumanMessage, SystemMessage

from article_state import ArticleState, update_state


class TitleGenerator:
    """标题生成器"""
    
    def __init__(self, model_name: str = "gpt-3.5-turbo"):
        self.llm = ChatOpenAI(
            model=model_name,
            temperature=0.8,  # 提高创造性
            api_key=os.getenv("OPENAI_API_KEY"),
            base_url=os.getenv("OPENAI_BASE_URL")
        )
        
        self.prompt_template = ChatPromptTemplate.from_messages([
            ("system", """你是一位专业的内容创作专家，擅长创作吸引人的爆款标题。

请根据给定的主题，生成3个不同风格的爆款标题：
1. 第一个标题要有悬念感，激发好奇心
2. 第二个标题要有实用性，突出价值
3. 第三个标题要有情感共鸣，引起共鸣

标题要求：
- 字数控制在15-25字之间
- 语言生动有趣，避免平淡无奇
- 符合网络传播特点
- 避免标题党，内容要有实质

请按以下格式输出：
标题1：[悬念型标题]
标题2：[实用型标题]  
标题3：[情感型标题]"""),
            ("human", "主题：{topic}")
        ])
    
    def generate_titles(self, topic: str) -> str:
        """生成标题"""
        try:
            messages = self.prompt_template.format_messages(topic=topic)
            response = self.llm.invoke(messages)
            return response.content
        except Exception as e:
            return f"标题生成失败：{str(e)}"


def title_node(state: ArticleState) -> ArticleState:
    """标题生成节点"""
    print(f"🎯 开始生成标题，主题：{state['topic']}")
    
    try:
        # 初始化标题生成器
        generator = TitleGenerator()
        
        # 生成标题
        titles_text = generator.generate_titles(state["topic"])
        
        # 更新状态
        new_state = update_state(
            state,
            title=titles_text,
            current_step="title_generated"
        )
        
        print(f"✅ 标题生成完成")
        print(f"生成的标题：\n{titles_text}")
        
        return new_state
        
    except Exception as e:
        error_msg = f"标题生成节点错误：{str(e)}"
        print(f"❌ {error_msg}")
        
        errors = state.get("errors", []) or []
        errors.append(error_msg)
        
        return update_state(
            state,
            errors=errors,
            current_step="title_error"
        )
