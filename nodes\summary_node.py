"""
摘要生成节点
"""
import os
from typing import Dict, Any
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate

from article_state import ArticleState, update_state


class SummaryGenerator:
    """摘要生成器"""
    
    def __init__(self, model_name: str = "gpt-3.5-turbo"):
        self.llm = ChatOpenAI(
            model=model_name,
            temperature=0.5,
            api_key=os.getenv("OPENAI_API_KEY"),
            base_url=os.getenv("OPENAI_BASE_URL")
        )
        
        self.prompt_template = ChatPromptTemplate.from_messages([
            ("system", """你是一位专业的内容编辑，擅长提炼文章精华，创作高质量的摘要。

请根据给定的文章正文，生成一个简洁而全面的摘要。

摘要要求：
1. 字数控制在150-200字之间
2. 准确概括文章的核心观点和主要内容
3. 语言简洁明了，逻辑清晰
4. 突出文章的价值和亮点
5. 适合作为文章的导读或分享时的介绍

摘要结构建议：
- 第一句：点明文章主题
- 中间部分：概括主要观点或要点
- 最后：总结价值或意义

请直接输出摘要内容，不需要额外的格式说明。"""),
            ("human", """请为以下文章生成摘要：

标题：{title}

正文：
{content}

请生成摘要：""")
        ])
    
    def generate_summary(self, title: str, content: str) -> str:
        """生成摘要"""
        try:
            messages = self.prompt_template.format_messages(
                title=title,
                content=content
            )
            response = self.llm.invoke(messages)
            return response.content
        except Exception as e:
            return f"摘要生成失败：{str(e)}"


def summary_node(state: ArticleState) -> ArticleState:
    """摘要生成节点"""
    print(f"📋 开始生成摘要")
    
    try:
        # 检查是否有正文
        if not state.get("content"):
            error_msg = "缺少正文内容，无法生成摘要"
            print(f"❌ {error_msg}")
            
            errors = state.get("errors", []) or []
            errors.append(error_msg)
            
            return update_state(
                state,
                errors=errors,
                current_step="summary_error"
            )
        
        # 初始化摘要生成器
        generator = SummaryGenerator()
        
        # 生成摘要
        summary_text = generator.generate_summary(
            state.get("title", ""),
            state["content"]
        )
        
        # 更新状态
        new_state = update_state(
            state,
            summary=summary_text,
            current_step="summary_generated"
        )
        
        print(f"✅ 摘要生成完成")
        print(f"摘要长度：{len(summary_text)}字")
        print(f"摘要内容：\n{summary_text}")
        
        return new_state
        
    except Exception as e:
        error_msg = f"摘要生成节点错误：{str(e)}"
        print(f"❌ {error_msg}")
        
        errors = state.get("errors", []) or []
        errors.append(error_msg)
        
        return update_state(
            state,
            errors=errors,
            current_step="summary_error"
        )
